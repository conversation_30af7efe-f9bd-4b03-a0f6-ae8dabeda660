// Database connectivity and CRUD operations test page
import { useState } from 'react';
import { GlassCard } from '@/components/GlassCard';
import { BottomNav } from '@/components/BottomNav';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useQuery } from '@tanstack/react-query';
import { 
  Database, 
  CheckCircle, 
  XCircle, 
  Clock, 
  User, 
  Activity, 
  Users, 
  MessageSquare,
  Wallet,
  Trophy,
  Loader2
} from 'lucide-react';

interface TestResult {
  table: string;
  operation: string;
  status: 'success' | 'error' | 'pending';
  message: string;
  timestamp: Date;
  duration?: number;
}

export default function DatabaseTest() {
  const { user } = useAuth();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);

  const addResult = (result: Omit<TestResult, 'timestamp'>) => {
    setTestResults(prev => [...prev, { ...result, timestamp: new Date() }]);
  };

  // Test database connection
  const testConnection = async (): Promise<boolean> => {
    const startTime = Date.now();
    try {
      const { data, error } = await supabase.from('profiles').select('count').limit(1);
      const duration = Date.now() - startTime;
      
      if (error) {
        addResult({
          table: 'connection',
          operation: 'ping',
          status: 'error',
          message: `Connection failed: ${error.message}`,
          duration,
        });
        return false;
      }
      
      addResult({
        table: 'connection',
        operation: 'ping',
        status: 'success',
        message: `Connection successful (${duration}ms)`,
        duration,
      });
      return true;
    } catch (error) {
      const duration = Date.now() - startTime;
      addResult({
        table: 'connection',
        operation: 'ping',
        status: 'error',
        message: `Connection error: ${error}`,
        duration,
      });
      return false;
    }
  };

  // Test profiles table
  const testProfiles = async () => {
    if (!user) return;
    
    const startTime = Date.now();
    try {
      // Test READ
      const { data: profile, error: readError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();
      
      if (readError) {
        addResult({
          table: 'profiles',
          operation: 'READ',
          status: 'error',
          message: readError.message,
          duration: Date.now() - startTime,
        });
        return;
      }
      
      addResult({
        table: 'profiles',
        operation: 'READ',
        status: 'success',
        message: `Profile loaded: ${profile?.username || 'No username'}`,
        duration: Date.now() - startTime,
      });

      // Test UPDATE (if profile exists)
      if (profile) {
        const updateStartTime = Date.now();
        const testUpdate = { updated_at: new Date().toISOString() };
        
        const { error: updateError } = await supabase
          .from('profiles')
          .update(testUpdate)
          .eq('id', user.id);
        
        if (updateError) {
          addResult({
            table: 'profiles',
            operation: 'UPDATE',
            status: 'error',
            message: updateError.message,
            duration: Date.now() - updateStartTime,
          });
        } else {
          addResult({
            table: 'profiles',
            operation: 'UPDATE',
            status: 'success',
            message: 'Profile updated successfully',
            duration: Date.now() - updateStartTime,
          });
        }
      }
    } catch (error) {
      addResult({
        table: 'profiles',
        operation: 'READ',
        status: 'error',
        message: `Unexpected error: ${error}`,
        duration: Date.now() - startTime,
      });
    }
  };

  // Test user_activities table
  const testUserActivities = async () => {
    if (!user) return;
    
    const startTime = Date.now();
    try {
      // Test READ
      const { data: activities, error: readError } = await supabase
        .from('user_activities')
        .select('*')
        .eq('user_id', user.id)
        .limit(5);
      
      if (readError) {
        addResult({
          table: 'user_activities',
          operation: 'READ',
          status: 'error',
          message: readError.message,
          duration: Date.now() - startTime,
        });
        return;
      }
      
      addResult({
        table: 'user_activities',
        operation: 'READ',
        status: 'success',
        message: `Found ${activities?.length || 0} activities`,
        duration: Date.now() - startTime,
      });

      // Test CREATE (insert test activity)
      const createStartTime = Date.now();
      const testActivity = {
        user_id: user.id,
        distance_km: 0.1,
        activity_type: 'Test',
        activity_date: new Date().toISOString().split('T')[0],
      };
      
      const { data: newActivity, error: createError } = await supabase
        .from('user_activities')
        .insert(testActivity)
        .select()
        .single();
      
      if (createError) {
        addResult({
          table: 'user_activities',
          operation: 'CREATE',
          status: 'error',
          message: createError.message,
          duration: Date.now() - createStartTime,
        });
        return;
      }
      
      addResult({
        table: 'user_activities',
        operation: 'CREATE',
        status: 'success',
        message: 'Test activity created',
        duration: Date.now() - createStartTime,
      });

      // Test DELETE (remove test activity)
      if (newActivity) {
        const deleteStartTime = Date.now();
        const { error: deleteError } = await supabase
          .from('user_activities')
          .delete()
          .eq('id', newActivity.id);
        
        if (deleteError) {
          addResult({
            table: 'user_activities',
            operation: 'DELETE',
            status: 'error',
            message: deleteError.message,
            duration: Date.now() - deleteStartTime,
          });
        } else {
          addResult({
            table: 'user_activities',
            operation: 'DELETE',
            status: 'success',
            message: 'Test activity deleted',
            duration: Date.now() - deleteStartTime,
          });
        }
      }
    } catch (error) {
      addResult({
        table: 'user_activities',
        operation: 'CRUD',
        status: 'error',
        message: `Unexpected error: ${error}`,
        duration: Date.now() - startTime,
      });
    }
  };

  // Test other tables
  const testOtherTables = async () => {
    const tables = [
      { name: 'guilds', icon: Users },
      { name: 'messages', icon: MessageSquare },
      { name: 'wallets', icon: Wallet },
      { name: 'user_leaderboard_stats', icon: Trophy },
    ];

    for (const table of tables) {
      const startTime = Date.now();
      try {
        const { data, error } = await supabase
          .from(table.name)
          .select('*')
          .limit(1);
        
        if (error) {
          addResult({
            table: table.name,
            operation: 'READ',
            status: 'error',
            message: error.message,
            duration: Date.now() - startTime,
          });
        } else {
          addResult({
            table: table.name,
            operation: 'READ',
            status: 'success',
            message: `Table accessible (${data?.length || 0} records)`,
            duration: Date.now() - startTime,
          });
        }
      } catch (error) {
        addResult({
          table: table.name,
          operation: 'READ',
          status: 'error',
          message: `Unexpected error: ${error}`,
          duration: Date.now() - startTime,
        });
      }
    }
  };

  const runAllTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);
    
    addResult({
      table: 'system',
      operation: 'START',
      status: 'success',
      message: 'Starting database connectivity tests...',
    });

    // Test connection first
    const isConnected = await testConnection();
    
    if (isConnected) {
      await testProfiles();
      await testUserActivities();
      await testOtherTables();
    }
    
    addResult({
      table: 'system',
      operation: 'COMPLETE',
      status: 'success',
      message: 'Database tests completed',
    });
    
    setIsRunningTests(false);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-400';
      case 'error':
        return 'text-red-400';
      case 'pending':
        return 'text-yellow-400';
    }
  };

  return (
    <>
      <div
        style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
        className="fixed inset-0 bg-center bg-cover filter blur-sm scale-105"
      />
      <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70">
        <div className="px-4 space-y-6">
          <h1 className="gradient-title text-2xl mb-4 text-center">
            Database Connectivity Test
          </h1>

          {/* User Info */}
          {user && (
            <Card className="bg-glass border-white/10">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <User className="h-5 w-5" />
                  Current User
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-white/70 text-sm font-mono">
                  ID: {user.id}
                </div>
                <div className="text-white/70 text-sm">
                  Email: {user.email || 'N/A'}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Test Controls */}
          <GlassCard className="p-6">
            <h2 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
              <Database className="h-5 w-5" />
              Database Tests
            </h2>
            <Button
              onClick={runAllTests}
              disabled={isRunningTests || !user}
              className="w-full bg-electric hover:bg-purple text-white"
            >
              {isRunningTests ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Running Tests...
                </>
              ) : (
                'Run Database Tests'
              )}
            </Button>
            {!user && (
              <p className="text-white/60 text-sm mt-2 text-center">
                Please log in to run database tests
              </p>
            )}
          </GlassCard>

          {/* Test Results */}
          {testResults.length > 0 && (
            <GlassCard className="p-6">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Test Results ({testResults.length})
              </h3>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between bg-black/20 p-3 rounded"
                  >
                    <div className="flex items-center gap-3">
                      {getStatusIcon(result.status)}
                      <div>
                        <div className="text-white font-medium">
                          {result.table}.{result.operation}
                        </div>
                        <div className={`text-sm ${getStatusColor(result.status)}`}>
                          {result.message}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-white/60 text-xs">
                        {result.timestamp.toLocaleTimeString()}
                      </div>
                      {result.duration && (
                        <div className="text-white/40 text-xs">
                          {result.duration}ms
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </GlassCard>
          )}
        </div>
        <BottomNav />
      </div>
    </>
  );
}
