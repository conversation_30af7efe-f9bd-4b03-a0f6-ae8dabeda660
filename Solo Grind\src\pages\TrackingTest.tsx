// Test page for verifying tracking and analytics functionality
import { useState } from 'react';
import { GlassCard } from '@/components/GlassCard';
import { BottomNav } from '@/components/BottomNav';
import { Button } from '@/components/ui/button';
import { useTracking } from '@/hooks/useTracking';
import { logger } from '@/lib/logger';
import { analytics } from '@/lib/analytics';
import { config, getEnvironmentInfo } from '@/lib/config';
import { useAuth } from '@/contexts/AuthContext';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Activity, 
  BarChart3, 
  Bug, 
  CheckCircle, 
  Clock, 
  Database, 
  Settings, 
  User,
  Zap
} from 'lucide-react';

export default function TrackingTest() {
  const { user, profile } = useAuth();
  const {
    trackAction,
    trackButtonClick,
    trackWorkoutCompletion,
    trackAchievementUnlock,
    trackGuildActivity,
    trackSubscriptionEvent,
    trackError,
    trackPerformanceMetric,
  } = useTracking();

  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunningTests, setIsRunningTests] = useState(false);

  const addResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const runTrackingTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);
    addResult('Starting tracking system tests...');

    try {
      // Test basic tracking
      trackAction('test_action', { test: true });
      addResult('✅ Basic action tracking test completed');

      // Test button click tracking
      trackButtonClick('test_button', { location: 'tracking_test_page' });
      addResult('✅ Button click tracking test completed');

      // Test workout tracking
      trackWorkoutCompletion({
        type: 'Test Run',
        distance: 5.2,
        duration: 1800, // 30 minutes
        calories: 300,
      });
      addResult('✅ Workout tracking test completed');

      // Test achievement tracking
      trackAchievementUnlock({
        achievementId: 'test_achievement',
        achievementName: 'Test Achievement',
        tier: 'Bronze',
      });
      addResult('✅ Achievement tracking test completed');

      // Test guild activity tracking
      trackGuildActivity('test_join', {
        guildId: 'test_guild_123',
        guildName: 'Test Guild',
      });
      addResult('✅ Guild activity tracking test completed');

      // Test subscription tracking
      trackSubscriptionEvent('test_upgrade', {
        plan: 'Pro',
        amount: 1000,
      });
      addResult('✅ Subscription tracking test completed');

      // Test error tracking
      trackError('Test error for tracking verification', {
        component: 'TrackingTest',
        severity: 'low',
      });
      addResult('✅ Error tracking test completed');

      // Test performance tracking
      trackPerformanceMetric('test_load_time', 150, {
        component: 'TrackingTest',
      });
      addResult('✅ Performance tracking test completed');

      addResult('🎉 All tracking tests completed successfully!');
    } catch (error) {
      addResult(`❌ Test failed: ${error}`);
    } finally {
      setIsRunningTests(false);
    }
  };

  const exportLogs = () => {
    const logs = logger.exportLogs();
    const blob = new Blob([logs], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sologrind-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    trackAction('export_logs', { timestamp: new Date().toISOString() });
    addResult('📁 Logs exported successfully');
  };

  const clearLogs = () => {
    logger.clearLogs();
    setTestResults([]);
    addResult('🗑️ Logs and test results cleared');
  };

  const envInfo = getEnvironmentInfo();

  return (
    <>
      <div
        style={{ backgroundImage: "url('https://images.unsplash.com/photo-1470813740244-df37b8c1edcb')" }}
        className="fixed inset-0 bg-center bg-cover filter blur-sm scale-105"
      />
      <div className="relative z-10 pb-20 pt-6 min-h-screen bg-black/70">
        <div className="px-4 space-y-6">
          <h1 className="gradient-title text-2xl mb-4 text-center">
            Tracking System Test
          </h1>

          {/* Environment Info */}
          <Card className="bg-glass border-white/10">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-white">
                <Settings className="h-5 w-5" />
                Environment Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-white/70">Environment:</span>
                <Badge variant={envInfo.isProd ? 'destructive' : 'secondary'}>
                  {envInfo.environment}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70">Analytics:</span>
                <Badge variant={envInfo.features.analytics ? 'default' : 'secondary'}>
                  {envInfo.features.analytics ? 'Enabled' : 'Disabled'}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70">Debug Mode:</span>
                <Badge variant={envInfo.features.debugMode ? 'default' : 'secondary'}>
                  {envInfo.features.debugMode ? 'Enabled' : 'Disabled'}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-white/70">Subscriptions:</span>
                <Badge variant={envInfo.features.subscriptions ? 'default' : 'secondary'}>
                  {envInfo.features.subscriptions ? 'Enabled' : 'Disabled'}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* User Info */}
          {user && (
            <Card className="bg-glass border-white/10">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <User className="h-5 w-5" />
                  User Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-white/70">User ID:</span>
                  <span className="text-white text-sm font-mono">{user.id.slice(0, 8)}...</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Username:</span>
                  <span className="text-white">{profile?.username || 'N/A'}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-white/70">Email:</span>
                  <span className="text-white">{user.email || 'N/A'}</span>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Test Controls */}
          <GlassCard className="p-6">
            <h2 className="text-xl font-bold text-white mb-4 flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Tracking Tests
            </h2>
            <div className="space-y-4">
              <Button
                onClick={runTrackingTests}
                disabled={isRunningTests}
                className="w-full bg-electric hover:bg-purple text-white"
              >
                {isRunningTests ? 'Running Tests...' : 'Run All Tracking Tests'}
              </Button>
              
              <div className="grid grid-cols-2 gap-2">
                <Button
                  onClick={exportLogs}
                  variant="outline"
                  className="text-white border-white/20"
                >
                  Export Logs
                </Button>
                <Button
                  onClick={clearLogs}
                  variant="outline"
                  className="text-white border-white/20"
                >
                  Clear Logs
                </Button>
              </div>
            </div>
          </GlassCard>

          {/* Test Results */}
          {testResults.length > 0 && (
            <GlassCard className="p-6">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Test Results
              </h3>
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {testResults.map((result, index) => (
                  <div
                    key={index}
                    className="text-sm text-white/80 font-mono bg-black/20 p-2 rounded"
                  >
                    {result}
                  </div>
                ))}
              </div>
            </GlassCard>
          )}

          {/* Quick Actions */}
          <GlassCard className="p-6">
            <h3 className="text-lg font-bold text-white mb-4">Quick Test Actions</h3>
            <div className="grid grid-cols-2 gap-2">
              <Button
                onClick={() => {
                  trackAction('quick_test_action');
                  addResult('Quick action tracked');
                }}
                variant="outline"
                size="sm"
                className="text-white border-white/20"
              >
                Test Action
              </Button>
              <Button
                onClick={() => {
                  trackError('Quick test error');
                  addResult('Test error tracked');
                }}
                variant="outline"
                size="sm"
                className="text-white border-white/20"
              >
                Test Error
              </Button>
            </div>
          </GlassCard>
        </div>
        <BottomNav />
      </div>
    </>
  );
}
