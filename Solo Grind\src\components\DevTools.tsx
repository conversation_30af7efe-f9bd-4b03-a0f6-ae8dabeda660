// Development tools component - only visible in development mode
import { useState } from 'react';
import { Link } from 'react-router-dom';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { 
  Settings, 
  Database, 
  BarChart3, 
  Bug, 
  Monitor,
  Code,
  ChevronDown,
  ChevronUp,
  ExternalLink
} from 'lucide-react';
import { isDevelopment, getEnvironmentInfo } from '@/lib/config';
import { performanceMonitor } from '@/lib/performance';
import { logger } from '@/lib/logger';

export const DevTools = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  if (!isDevelopment()) {
    return null;
  }

  const envInfo = getEnvironmentInfo();
  const performanceSummary = performanceMonitor.getPerformanceSummary();
  const recentLogs = logger.getLogs().slice(-5);

  const exportData = () => {
    const data = {
      environment: envInfo,
      performance: performanceSummary,
      logs: logger.getLogs(),
      timestamp: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sologrind-dev-data-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (!isExpanded) {
    return (
      <div className="fixed bottom-20 right-4 z-50">
        <Button
          onClick={() => setIsExpanded(true)}
          size="sm"
          className="bg-purple/80 hover:bg-purple text-white shadow-lg"
        >
          <Code className="h-4 w-4 mr-2" />
          Dev Tools
          <ChevronUp className="h-4 w-4 ml-2" />
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-20 right-4 z-50 w-80 max-h-96 overflow-y-auto">
      <Card className="bg-black/90 border-purple/50 shadow-xl">
        <CardHeader className="pb-2">
          <CardTitle className="flex items-center justify-between text-white text-sm">
            <div className="flex items-center gap-2">
              <Code className="h-4 w-4" />
              Development Tools
            </div>
            <Button
              onClick={() => setIsExpanded(false)}
              size="sm"
              variant="ghost"
              className="h-6 w-6 p-0 text-white/60 hover:text-white"
            >
              <ChevronDown className="h-4 w-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-4 text-sm">
          {/* Environment Status */}
          <div>
            <h4 className="text-white font-medium mb-2 flex items-center gap-2">
              <Settings className="h-3 w-3" />
              Environment
            </h4>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span className="text-white/70">Mode:</span>
                <Badge variant="secondary" className="text-xs">
                  {envInfo.environment}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">Analytics:</span>
                <Badge variant={envInfo.features.analytics ? 'default' : 'secondary'} className="text-xs">
                  {envInfo.features.analytics ? 'On' : 'Off'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">Debug:</span>
                <Badge variant={envInfo.features.debugMode ? 'default' : 'secondary'} className="text-xs">
                  {envInfo.features.debugMode ? 'On' : 'Off'}
                </Badge>
              </div>
            </div>
          </div>

          {/* Performance Summary */}
          <div>
            <h4 className="text-white font-medium mb-2 flex items-center gap-2">
              <Monitor className="h-3 w-3" />
              Performance
            </h4>
            <div className="space-y-1">
              {Object.entries(performanceSummary).slice(0, 3).map(([name, stats]) => (
                <div key={name} className="flex justify-between text-xs">
                  <span className="text-white/70 truncate">{name}:</span>
                  <span className="text-white">{Math.round(stats.avg)}ms</span>
                </div>
              ))}
              {Object.keys(performanceSummary).length === 0 && (
                <span className="text-white/50 text-xs">No metrics yet</span>
              )}
            </div>
          </div>

          {/* Recent Logs */}
          <div>
            <h4 className="text-white font-medium mb-2 flex items-center gap-2">
              <Bug className="h-3 w-3" />
              Recent Logs
            </h4>
            <div className="space-y-1 max-h-20 overflow-y-auto">
              {recentLogs.map((log, index) => (
                <div key={index} className="text-xs">
                  <span className="text-white/50">
                    {log.timestamp.toLocaleTimeString()}
                  </span>
                  <span className="text-white/70 ml-2 truncate block">
                    {log.message}
                  </span>
                </div>
              ))}
              {recentLogs.length === 0 && (
                <span className="text-white/50 text-xs">No logs yet</span>
              )}
            </div>
          </div>

          {/* Quick Actions */}
          <div>
            <h4 className="text-white font-medium mb-2">Quick Actions</h4>
            <div className="grid grid-cols-2 gap-2">
              <Link to="/tracking-test">
                <Button size="sm" variant="outline" className="w-full text-xs text-white border-white/20">
                  <BarChart3 className="h-3 w-3 mr-1" />
                  Analytics
                  <ExternalLink className="h-3 w-3 ml-1" />
                </Button>
              </Link>
              
              <Link to="/database-test">
                <Button size="sm" variant="outline" className="w-full text-xs text-white border-white/20">
                  <Database className="h-3 w-3 mr-1" />
                  Database
                  <ExternalLink className="h-3 w-3 ml-1" />
                </Button>
              </Link>
            </div>
            
            <Button
              onClick={exportData}
              size="sm"
              variant="outline"
              className="w-full mt-2 text-xs text-white border-white/20"
            >
              Export Dev Data
            </Button>
          </div>

          {/* Memory Usage */}
          <div>
            <h4 className="text-white font-medium mb-2">Memory</h4>
            <div className="text-xs">
              {(() => {
                const memory = performanceMonitor.getMemoryUsage();
                if (memory) {
                  const usedMB = Math.round(memory.used / 1024 / 1024);
                  const totalMB = Math.round(memory.total / 1024 / 1024);
                  return (
                    <span className="text-white/70">
                      {usedMB}MB / {totalMB}MB
                    </span>
                  );
                }
                return <span className="text-white/50">Not available</span>;
              })()}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
